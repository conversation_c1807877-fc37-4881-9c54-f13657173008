{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}جدول التركيبات - التعديل السريع{% endblock %}

{% block extra_css %}
<style>
    .quick-edit-table {
        font-size: 0.9rem;
    }
    
    .quick-edit-cell {
        padding: 5px !important;
        vertical-align: middle;
    }
    
    .quick-edit-select {
        width: 100%;
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 0.85rem;
    }
    
    .quick-edit-input {
        width: 100%;
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 0.85rem;
    }
    
    .quick-save-btn {
        padding: 2px 8px;
        font-size: 0.75rem;
        border-radius: 3px;
        margin: 1px;
    }
    
    .status-pending { background-color: #fff3cd; }
    .status-ready { background-color: #d1ecf1; }
    .status-scheduled { background-color: #d4edda; }
    .status-in_progress { background-color: #cce5ff; }
    .status-completed { background-color: #d4edda; }
    .status-cancelled { background-color: #f8d7da; }
    
    .priority-urgent { border-left: 4px solid #dc3545; }
    .priority-high { border-left: 4px solid #fd7e14; }
    .priority-normal { border-left: 4px solid #28a745; }
    .priority-low { border-left: 4px solid #6c757d; }
    
    .table-responsive {
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .sticky-header th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .bulk-actions {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 15px;
    }
    
    .filter-section {
        background: #ffffff;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-edit"></i> جدول التركيبات - التعديل السريع</h1>
        <div>
            <button class="btn btn-success" id="save-all-btn">
                <i class="fas fa-save"></i> حفظ جميع التغييرات
            </button>
            <button class="btn btn-secondary" id="refresh-btn">
                <i class="fas fa-sync"></i> تحديث
            </button>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label>البحث:</label>
                <input type="text" class="form-control" id="search-input" placeholder="اسم العميل أو رقم الهاتف">
            </div>
            <div class="col-md-2">
                <label>الحالة:</label>
                <select class="form-control" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="ready">جاهز</option>
                    <option value="scheduled">مجدول</option>
                    <option value="in_progress">جاري التنفيذ</option>
                    <option value="completed">مكتمل</option>
                    <option value="cancelled">ملغي</option>
                </select>
            </div>
            <div class="col-md-2">
                <label>الأولوية:</label>
                <select class="form-control" id="priority-filter">
                    <option value="">جميع الأولويات</option>
                    <option value="urgent">عاجل</option>
                    <option value="high">عالية</option>
                    <option value="normal">عادية</option>
                    <option value="low">منخفضة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label>الفرع:</label>
                <select class="form-control" id="branch-filter">
                    <option value="">جميع الفروع</option>
                    {% for branch in branches %}
                    <option value="{{ branch }}">{{ branch }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>التاريخ:</label>
                <input type="date" class="form-control" id="date-filter">
            </div>
        </div>
    </div>

    <!-- إجراءات مجمعة -->
    <div class="bulk-actions">
        <div class="row align-items-end">
            <div class="col-md-2">
                <label>إجراءات مجمعة:</label>
                <select class="form-control" id="bulk-action">
                    <option value="">اختر إجراء</option>
                    <option value="update_status">تحديث الحالة</option>
                    <option value="update_priority">تحديث الأولوية</option>
                    <option value="assign_team">تعيين فريق</option>
                    <option value="schedule_date">تحديد موعد</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-control" id="bulk-value" style="display: none;">
                    <!-- سيتم ملؤها ديناميكياً -->
                </select>
                <input type="date" class="form-control" id="bulk-date" style="display: none;">
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary" id="apply-bulk-btn" disabled>
                    <i class="fas fa-check"></i> تطبيق
                </button>
            </div>
            <div class="col-md-6 text-right">
                <small class="text-muted">
                    <span id="selected-count">0</span> عنصر محدد من أصل 
                    <span id="total-count">{{ installations.count }}</span>
                </small>
            </div>
        </div>
    </div>

    <!-- الجدول -->
    <div class="table-responsive">
        <div class="loading-overlay" id="loading-overlay">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">جاري التحميل...</span>
            </div>
        </div>
        
        <table class="table table-sm table-hover quick-edit-table">
            <thead class="sticky-header">
                <tr>
                    <th width="30">
                        <input type="checkbox" id="select-all">
                    </th>
                    <th width="80">رقم التركيب</th>
                    <th width="150">اسم العميل</th>
                    <th width="120">رقم الهاتف</th>
                    <th width="100">عدد الشبابيك</th>
                    <th width="120">الحالة</th>
                    <th width="100">الأولوية</th>
                    <th width="150">الفريق</th>
                    <th width="120">تاريخ الجدولة</th>
                    <th width="100">وقت البدء</th>
                    <th width="100">وقت الانتهاء</th>
                    <th width="100">جاهز للتركيب</th>
                    <th width="80">إجراءات</th>
                </tr>
            </thead>
            <tbody id="installations-tbody">
                {% for installation in installations %}
                <tr class="installation-row status-{{ installation.status }} priority-{{ installation.priority }}" 
                    data-id="{{ installation.id }}">
                    <td class="quick-edit-cell">
                        <input type="checkbox" class="row-select" value="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <strong>#{{ installation.id }}</strong>
                    </td>
                    <td class="quick-edit-cell">
                        <span title="{{ installation.customer_address }}">
                            {{ installation.customer_name|truncatechars:20 }}
                        </span>
                    </td>
                    <td class="quick-edit-cell">{{ installation.customer_phone }}</td>
                    <td class="quick-edit-cell">
                        <input type="number" class="quick-edit-input windows-count" 
                               value="{{ installation.windows_count }}" min="1" max="50"
                               data-field="windows_count" data-id="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <select class="quick-edit-select status-select" 
                                data-field="status" data-id="{{ installation.id }}">
                            <option value="pending" {% if installation.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="ready" {% if installation.status == 'ready' %}selected{% endif %}>جاهز</option>
                            <option value="scheduled" {% if installation.status == 'scheduled' %}selected{% endif %}>مجدول</option>
                            <option value="in_progress" {% if installation.status == 'in_progress' %}selected{% endif %}>جاري التنفيذ</option>
                            <option value="completed" {% if installation.status == 'completed' %}selected{% endif %}>مكتمل</option>
                            <option value="cancelled" {% if installation.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </td>
                    <td class="quick-edit-cell">
                        <select class="quick-edit-select priority-select" 
                                data-field="priority" data-id="{{ installation.id }}">
                            <option value="low" {% if installation.priority == 'low' %}selected{% endif %}>منخفضة</option>
                            <option value="normal" {% if installation.priority == 'normal' %}selected{% endif %}>عادية</option>
                            <option value="high" {% if installation.priority == 'high' %}selected{% endif %}>عالية</option>
                            <option value="urgent" {% if installation.priority == 'urgent' %}selected{% endif %}>عاجل</option>
                        </select>
                    </td>
                    <td class="quick-edit-cell">
                        <select class="quick-edit-select team-select" 
                                data-field="team" data-id="{{ installation.id }}">
                            <option value="">غير محدد</option>
                            {% for team in teams %}
                            <option value="{{ team.id }}" 
                                {% if installation.team and installation.team.id == team.id %}selected{% endif %}>
                                {{ team.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </td>
                    <td class="quick-edit-cell">
                        <input type="date" class="quick-edit-input scheduled-date" 
                               value="{{ installation.scheduled_date|date:'Y-m-d' }}"
                               data-field="scheduled_date" data-id="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <input type="time" class="quick-edit-input scheduled-time-start" 
                               value="{{ installation.scheduled_time_start|time:'H:i' }}"
                               data-field="scheduled_time_start" data-id="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <input type="time" class="quick-edit-input scheduled-time-end" 
                               value="{{ installation.scheduled_time_end|time:'H:i' }}"
                               data-field="scheduled_time_end" data-id="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <input type="checkbox" class="ready-checkbox" 
                               {% if installation.is_ready_for_installation %}checked{% endif %}
                               data-field="is_ready_for_installation" data-id="{{ installation.id }}">
                    </td>
                    <td class="quick-edit-cell">
                        <button class="btn btn-sm btn-success quick-save-btn" 
                                data-id="{{ installation.id }}" title="حفظ">
                            <i class="fas fa-save"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="13" class="text-center">لا توجد تركيبات</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <h5 class="text-warning" id="pending-count">{{ stats.pending }}</h5>
                            <small>قيد الانتظار</small>
                        </div>
                        <div class="col-md-2">
                            <h5 class="text-info" id="ready-count">{{ stats.ready }}</h5>
                            <small>جاهز</small>
                        </div>
                        <div class="col-md-2">
                            <h5 class="text-primary" id="scheduled-count">{{ stats.scheduled }}</h5>
                            <small>مجدول</small>
                        </div>
                        <div class="col-md-2">
                            <h5 class="text-secondary" id="in-progress-count">{{ stats.in_progress }}</h5>
                            <small>جاري التنفيذ</small>
                        </div>
                        <div class="col-md-2">
                            <h5 class="text-success" id="completed-count">{{ stats.completed }}</h5>
                            <small>مكتمل</small>
                        </div>
                        <div class="col-md-2">
                            <h5 class="text-danger" id="cancelled-count">{{ stats.cancelled }}</h5>
                            <small>ملغي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let changedRows = new Set();
    let selectedRows = new Set();

    // تتبع التغييرات
    $('.quick-edit-input, .quick-edit-select, .ready-checkbox').on('change', function() {
        const id = $(this).data('id');
        changedRows.add(id);
        $(this).closest('tr').addClass('table-warning');
        updateSaveButton(id);
    });

    // تحديث زر الحفظ
    function updateSaveButton(id) {
        const saveBtn = $(`.quick-save-btn[data-id="${id}"]`);
        saveBtn.removeClass('btn-success').addClass('btn-warning');
        saveBtn.find('i').removeClass('fa-save').addClass('fa-exclamation');
    }

    // حفظ تركيب واحد
    $('.quick-save-btn').on('click', function() {
        const id = $(this).data('id');
        saveInstallation(id);
    });

    // حفظ جميع التغييرات
    $('#save-all-btn').on('click', function() {
        if (changedRows.size === 0) {
            showAlert('لا توجد تغييرات للحفظ', 'info');
            return;
        }

        const confirmMsg = `هل تريد حفظ التغييرات على ${changedRows.size} تركيب؟`;
        if (confirm(confirmMsg)) {
            saveAllChanges();
        }
    });

    // حفظ تركيب واحد
    function saveInstallation(id) {
        const row = $(`.installation-row[data-id="${id}"]`);
        const data = collectRowData(row);

        showLoading(true);

        $.ajax({
            url: `/installations/api/quick-update/${id}/`,
            method: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            },
            success: function(response) {
                if (response.success) {
                    changedRows.delete(id);
                    row.removeClass('table-warning');
                    const saveBtn = $(`.quick-save-btn[data-id="${id}"]`);
                    saveBtn.removeClass('btn-warning').addClass('btn-success');
                    saveBtn.find('i').removeClass('fa-exclamation').addClass('fa-save');

                    // تحديث الصف بالبيانات الجديدة
                    updateRowClass(row, data.status, data.priority);

                    showAlert('تم حفظ التغييرات بنجاح', 'success');
                    updateStats();
                } else {
                    showAlert('خطأ في حفظ البيانات: ' + response.error, 'error');
                }
            },
            error: function(xhr) {
                showAlert('خطأ في الاتصال بالخادم', 'error');
            },
            complete: function() {
                showLoading(false);
            }
        });
    }

    // جمع بيانات الصف
    function collectRowData(row) {
        return {
            windows_count: parseInt(row.find('.windows-count').val()) || 0,
            status: row.find('.status-select').val(),
            priority: row.find('.priority-select').val(),
            team: row.find('.team-select').val() || null,
            scheduled_date: row.find('.scheduled-date').val() || null,
            scheduled_time_start: row.find('.scheduled-time-start').val() || null,
            scheduled_time_end: row.find('.scheduled-time-end').val() || null,
            is_ready_for_installation: row.find('.ready-checkbox').is(':checked')
        };
    }

    // تحديث class الصف
    function updateRowClass(row, status, priority) {
        // إزالة classes القديمة
        row.removeClass(function(index, className) {
            return (className.match(/(^|\s)status-\S+/g) || []).join(' ');
        });
        row.removeClass(function(index, className) {
            return (className.match(/(^|\s)priority-\S+/g) || []).join(' ');
        });

        // إضافة classes جديدة
        row.addClass(`status-${status} priority-${priority}`);
    }

    // وظائف مساعدة
    function showLoading(show) {
        if (show) {
            $('#loading-overlay').show();
        } else {
            $('#loading-overlay').hide();
        }
    }

    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        $('.container-fluid').prepend(alertHtml);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function updateStats() {
        // تحديث الإحصائيات (يمكن تحسينها لاحقاً)
        location.reload();
    }
});
</script>
{% endblock %}
