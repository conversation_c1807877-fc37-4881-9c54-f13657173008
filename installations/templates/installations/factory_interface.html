{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}واجهة المصنع - إدارة التركيبات{% endblock %}

{% block extra_css %}
<style>
    .factory-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .factory-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
        border: 1px solid #e3f2fd;
    }
    
    .factory-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }
    
    .order-card {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .order-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: #6c757d;
        transition: all 0.3s ease;
    }
    
    .order-card.pending::before { background: #ffc107; }
    .order-card.in-production::before { background: #17a2b8; }
    .order-card.ready::before { background: #28a745; }
    .order-card.urgent::before { background: #dc3545; }
    
    .order-card:hover {
        border-color: #007bff;
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
    }
    
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .status-in-production { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .status-ready { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .status-urgent { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    
    .priority-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 8px;
    }
    
    .priority-urgent { background: #dc3545; }
    .priority-high { background: #fd7e14; }
    .priority-normal { background: #28a745; }
    .priority-low { background: #6c757d; }
    
    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .btn-factory {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }
    
    .btn-factory:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .production-timeline {
        position: relative;
        padding: 20px 0;
    }
    
    .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
    }
    
    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        font-size: 1.2rem;
        color: white;
        z-index: 2;
    }
    
    .timeline-content {
        flex: 1;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #007bff;
    }
    
    .timeline-item.completed .timeline-icon { background: #28a745; }
    .timeline-item.current .timeline-icon { background: #007bff; }
    .timeline-item.pending .timeline-icon { background: #6c757d; }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-top: 4px solid #007bff;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .search-section {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .bulk-actions {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        border: 2px dashed #dee2e6;
    }
    
    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .order-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-top: 15px;
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
    }
    
    .detail-icon {
        color: #6c757d;
        width: 16px;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .filter-tabs {
        background: white;
        border-radius: 12px;
        padding: 5px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .filter-tab {
        padding: 12px 20px;
        border: none;
        background: transparent;
        border-radius: 8px;
        margin: 0 5px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .filter-tab.active {
        background: #007bff;
        color: white;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }
    
    .filter-tab:hover:not(.active) {
        background: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="factory-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-industry"></i> واجهة المصنع
                </h1>
                <p class="mb-0 mt-2">إدارة حالة التركيبات وتحديث جاهزية الطلبات</p>
            </div>
            <div class="col-md-4 text-right">
                <div class="d-flex justify-content-end gap-2">
                    <button class="btn btn-light btn-factory" id="refresh-btn">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                    <button class="btn btn-warning btn-factory" id="export-btn">
                        <i class="fas fa-file-export"></i> تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number text-warning" id="pending-count">{{ stats.pending }}</div>
            <div class="stat-label">في الانتظار</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-info" id="production-count">{{ stats.in_production }}</div>
            <div class="stat-label">قيد الإنتاج</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-success" id="ready-count">{{ stats.ready }}</div>
            <div class="stat-label">جاهز للتركيب</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-danger" id="urgent-count">{{ stats.urgent }}</div>
            <div class="stat-label">طلبات عاجلة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-primary" id="total-windows">{{ stats.total_windows }}</div>
            <div class="stat-label">إجمالي الشبابيك</div>
        </div>
    </div>

    <!-- فلاتر التبويب -->
    <div class="filter-tabs">
        <button class="filter-tab active" data-status="all">
            جميع الطلبات <span class="badge badge-light ml-2">{{ stats.total }}</span>
        </button>
        <button class="filter-tab" data-status="pending">
            في الانتظار <span class="badge badge-warning ml-2">{{ stats.pending }}</span>
        </button>
        <button class="filter-tab" data-status="in_production">
            قيد الإنتاج <span class="badge badge-info ml-2">{{ stats.in_production }}</span>
        </button>
        <button class="filter-tab" data-status="ready">
            جاهز للتركيب <span class="badge badge-success ml-2">{{ stats.ready }}</span>
        </button>
        <button class="filter-tab" data-status="urgent">
            عاجل <span class="badge badge-danger ml-2">{{ stats.urgent }}</span>
        </button>
    </div>

    <!-- البحث والفلاتر -->
    <div class="search-section">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>البحث:</label>
                    <input type="text" class="form-control" id="search-input" 
                           placeholder="رقم الطلب، اسم العميل، أو رقم الهاتف">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>الفرع:</label>
                    <select class="form-control" id="branch-filter">
                        <option value="">جميع الفروع</option>
                        {% for branch in branches %}
                        <option value="{{ branch }}">{{ branch }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>الأولوية:</label>
                    <select class="form-control" id="priority-filter">
                        <option value="">جميع الأولويات</option>
                        <option value="urgent">عاجل</option>
                        <option value="high">عالية</option>
                        <option value="normal">عادية</option>
                        <option value="low">منخفضة</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>من تاريخ:</label>
                    <input type="date" class="form-control" id="date-from">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>إلى تاريخ:</label>
                    <input type="date" class="form-control" id="date-to">
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات مجمعة -->
    <div class="bulk-actions" style="display: none;" id="bulk-actions">
        <div class="row align-items-center">
            <div class="col-md-6">
                <span id="selected-count">0</span> طلب محدد
            </div>
            <div class="col-md-6 text-right">
                <button class="btn btn-info btn-sm" id="bulk-production-btn">
                    <i class="fas fa-cogs"></i> بدء الإنتاج
                </button>
                <button class="btn btn-success btn-sm" id="bulk-ready-btn">
                    <i class="fas fa-check"></i> جاهز للتركيب
                </button>
                <button class="btn btn-warning btn-sm" id="bulk-priority-btn">
                    <i class="fas fa-exclamation"></i> تحديد أولوية
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الطلبات -->
    <div class="factory-card">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> طلبات التركيب
            </h5>
            <div>
                <button class="btn btn-outline-primary btn-sm" id="select-all-btn">
                    <i class="fas fa-check-square"></i> تحديد الكل
                </button>
                <button class="btn btn-outline-secondary btn-sm" id="clear-selection-btn">
                    <i class="fas fa-times"></i> إلغاء التحديد
                </button>
            </div>
        </div>

        <div id="orders-container">
            {% for installation in installations %}
            <div class="order-card {{ installation.status }} {% if installation.priority == 'urgent' %}urgent{% endif %}" 
                 data-id="{{ installation.id }}" 
                 data-status="{{ installation.status }}"
                 data-priority="{{ installation.priority }}"
                 data-branch="{{ installation.branch_name }}">
                
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <input type="checkbox" class="order-checkbox mr-3" value="{{ installation.id }}">
                            <h6 class="mb-0">
                                طلب #{{ installation.id }} - {{ installation.customer_name }}
                                <span class="priority-indicator priority-{{ installation.priority }}" 
                                      title="{{ installation.get_priority_display }}"></span>
                            </h6>
                        </div>
                        
                        <div class="order-details">
                            <div class="detail-item">
                                <i class="fas fa-phone detail-icon"></i>
                                <span>{{ installation.customer_phone }}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-building detail-icon"></i>
                                <span>{{ installation.branch_name }}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-window-maximize detail-icon"></i>
                                <span>{{ installation.windows_count }} شباك</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-calendar detail-icon"></i>
                                <span>{{ installation.order_date|date:"Y/m/d" }}</span>
                            </div>
                        </div>
                        
                        {% if installation.notes %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note"></i> {{ installation.notes|truncatechars:100 }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="text-right">
                        <div class="mb-2">
                            <span class="status-badge status-{{ installation.status }}">
                                {% if installation.status == 'pending' %}في الانتظار
                                {% elif installation.status == 'in_production' %}قيد الإنتاج
                                {% elif installation.status == 'ready' %}جاهز
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="action-buttons">
                            {% if installation.status == 'pending' %}
                            <button class="btn btn-info btn-sm start-production-btn" data-id="{{ installation.id }}">
                                <i class="fas fa-play"></i> بدء الإنتاج
                            </button>
                            {% elif installation.status == 'in_production' %}
                            <button class="btn btn-success btn-sm mark-ready-btn" data-id="{{ installation.id }}">
                                <i class="fas fa-check"></i> جاهز للتركيب
                            </button>
                            {% endif %}
                            
                            <button class="btn btn-outline-primary btn-sm view-details-btn" data-id="{{ installation.id }}">
                                <i class="fas fa-eye"></i>
                            </button>
                            
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                        data-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="#" onclick="updatePriority({{ installation.id }}, 'urgent')">
                                        <i class="fas fa-exclamation-triangle text-danger"></i> عاجل
                                    </a>
                                    <a class="dropdown-item" href="#" onclick="updatePriority({{ installation.id }}, 'high')">
                                        <i class="fas fa-arrow-up text-warning"></i> أولوية عالية
                                    </a>
                                    <a class="dropdown-item" href="#" onclick="updatePriority({{ installation.id }}, 'normal')">
                                        <i class="fas fa-minus text-success"></i> أولوية عادية
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="#" onclick="addNotes({{ installation.id }})">
                                        <i class="fas fa-sticky-note"></i> إضافة ملاحظة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات</h5>
                <p class="text-muted">لا توجد طلبات تركيب متاحة حالياً</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="text-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">جاري التحميل...</span>
        </div>
        <div class="mt-3">جاري المعالجة...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let selectedOrders = new Set();

    // تحديد/إلغاء تحديد طلب واحد
    $('.order-checkbox').on('change', function() {
        const orderId = $(this).val();
        const isChecked = $(this).is(':checked');

        if (isChecked) {
            selectedOrders.add(orderId);
        } else {
            selectedOrders.delete(orderId);
        }

        updateBulkActions();
    });

    // تحديد الكل
    $('#select-all-btn').on('click', function() {
        $('.order-checkbox:visible').prop('checked', true);
        selectedOrders.clear();
        $('.order-checkbox:visible').each(function() {
            selectedOrders.add($(this).val());
        });
        updateBulkActions();
    });

    // إلغاء التحديد
    $('#clear-selection-btn').on('click', function() {
        $('.order-checkbox').prop('checked', false);
        selectedOrders.clear();
        updateBulkActions();
    });

    // تحديث الإجراءات المجمعة
    function updateBulkActions() {
        const count = selectedOrders.size;
        $('#selected-count').text(count);

        if (count > 0) {
            $('#bulk-actions').show();
        } else {
            $('#bulk-actions').hide();
        }
    }

    // بدء الإنتاج لطلب واحد
    $('.start-production-btn').on('click', function() {
        const orderId = $(this).data('id');
        updateOrderStatus(orderId, 'in_production');
    });

    // تحديد جاهز للتركيب لطلب واحد
    $('.mark-ready-btn').on('click', function() {
        const orderId = $(this).data('id');
        updateOrderStatus(orderId, 'ready');
    });

    // تحديث حالة طلب واحد
    function updateOrderStatus(orderId, newStatus) {
        $.ajax({
            url: `/installations/api/factory/update-status/${orderId}/`,
            method: 'POST',
            data: JSON.stringify({
                status: newStatus,
                is_ready_for_installation: newStatus === 'ready'
            }),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            },
            success: function(response) {
                if (response.success) {
                    updateOrderCard(orderId, newStatus);
                    showAlert('تم تحديث حالة الطلب بنجاح', 'success');
                } else {
                    showAlert('خطأ في تحديث الطلب: ' + response.error, 'error');
                }
            },
            error: function(xhr) {
                showAlert('خطأ في الاتصال بالخادم', 'error');
            }
        });
    }

    // تحديث بطاقة الطلب
    function updateOrderCard(orderId, newStatus) {
        const card = $(`.order-card[data-id="${orderId}"]`);
        card.removeClass('pending in-production ready').addClass(newStatus);

        const statusBadge = card.find('.status-badge');
        statusBadge.removeClass('status-pending status-in-production status-ready');
        statusBadge.addClass(`status-${newStatus}`);

        let statusText = '';
        switch(newStatus) {
            case 'pending': statusText = 'في الانتظار'; break;
            case 'in_production': statusText = 'قيد الإنتاج'; break;
            case 'ready': statusText = 'جاهز'; break;
        }
        statusBadge.text(statusText);
    }

    // وظائف مساعدة
    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        $('body').prepend(alertHtml);

        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
</script>
{% endblock %}
