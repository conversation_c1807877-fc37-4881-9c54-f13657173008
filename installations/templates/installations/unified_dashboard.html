{% extends "base.html" %}
{% load i18n %}

{% block title %}لوحة التحكم الموحدة{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 20px;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .stats-card.orders {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .stats-card.installations {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .stats-card.factory {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .stats-card.customers {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .quick-action-btn {
        border-radius: 10px;
        padding: 15px 25px;
        margin: 10px;
        transition: all 0.3s ease;
        border: none;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
    }
    
    .quick-action-btn:hover {
        transform: scale(1.05);
        text-decoration: none;
    }
    
    .activity-item {
        border-left: 4px solid #007bff;
        padding: 10px 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 10px 10px 0;
    }
    
    .alert-item {
        border-radius: 10px;
        margin-bottom: 10px;
    }
    
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 mb-0">
                <i class="fas fa-tachometer-alt text-primary"></i> لوحة التحكم الموحدة
            </h1>
            <p class="text-muted mb-0">نظام إدارة شامل لجميع الأقسام</p>
        </div>
        <div class="text-muted">
            <i class="fas fa-calendar"></i> {{ today|date:"Y-m-d" }}
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="dashboard-card stats-card orders">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-3x mb-3 opacity-75"></i>
                    <h3 class="mb-1">{{ quick_stats.total_orders_today }}</h3>
                    <p class="mb-0">طلبات اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6">
            <div class="dashboard-card stats-card installations">
                <div class="card-body text-center">
                    <i class="fas fa-tools fa-3x mb-3 opacity-75"></i>
                    <h3 class="mb-1">{{ quick_stats.pending_installations }}</h3>
                    <p class="mb-0">تركيبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6">
            <div class="dashboard-card stats-card factory">
                <div class="card-body text-center">
                    <i class="fas fa-industry fa-3x mb-3 opacity-75"></i>
                    <h3 class="mb-1">{{ quick_stats.active_production }}</h3>
                    <p class="mb-0">إنتاج نشط</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
                </div>
                <div class="card-body text-center">
                    <a href="/orders/create/" class="btn btn-primary quick-action-btn">
                        <i class="fas fa-plus"></i> طلب جديد
                    </a>
                    <a href="/installations/create/" class="btn btn-success quick-action-btn">
                        <i class="fas fa-tools"></i> تركيب جديد
                    </a>
                    <a href="/installations/calendar/" class="btn btn-secondary quick-action-btn">
                        <i class="fas fa-calendar"></i> التقويم
                    </a>
                    <a href="/installations/technician-analytics/" class="btn btn-info quick-action-btn">
                        <i class="fas fa-chart-bar"></i> تحليل الفنيين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Statistics -->
    <div class="row mb-4">
        <!-- Orders Statistics -->
        <div class="col-lg-3 col-md-6">
            <div class="dashboard-card">
                <div class="section-header">
                    <h6 class="mb-0"><i class="fas fa-shopping-cart"></i> الطلبات</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary">{{ orders_stats.total_count|default:0 }}</h5>
                            <small>الإجمالي</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ orders_stats.pending_count|default:0 }}</h5>
                            <small>معلق</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-info">{{ orders_stats.week_count|default:0 }}</h6>
                            <small>هذا الأسبوع</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success">{{ orders_stats.completed_count|default:0 }}</h6>
                            <small>مكتمل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installations Statistics -->
        <div class="col-lg-3 col-md-6">
            <div class="dashboard-card">
                <div class="section-header">
                    <h6 class="mb-0"><i class="fas fa-tools"></i> التركيبات</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary">{{ installations_stats.total_count|default:0 }}</h5>
                            <small>الإجمالي</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ installations_stats.ready_count|default:0 }}</h5>
                            <small>جاهز</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-info">{{ installations_stats.today_count|default:0 }}</h6>
                            <small>اليوم</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success">{{ installations_stats.completed_count|default:0 }}</h6>
                            <small>مكتمل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Factory Statistics -->
        <div class="col-lg-3 col-md-6">
            <div class="dashboard-card">
                <div class="section-header">
                    <h6 class="mb-0"><i class="fas fa-industry"></i> المصنع</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary">{{ factory_stats.total_orders|default:0 }}</h5>
                            <small>طلبات الإنتاج</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ factory_stats.active_orders|default:0 }}</h5>
                            <small>نشط</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-info">{{ factory_stats.active_lines|default:0 }}</h6>
                            <small>خطوط نشطة</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success">{{ factory_stats.completed_orders|default:0 }}</h6>
                            <small>مكتمل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Alerts and Activities -->
    <div class="row">
        <!-- System Alerts -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="section-header">
                    <h6 class="mb-0"><i class="fas fa-bell"></i> التنبيهات</h6>
                </div>
                <div class="card-body">
                    {% if alerts %}
                        {% for alert in alerts %}
                        <div class="alert alert-{{ alert.type }} alert-item">
                            <i class="{{ alert.icon }}"></i>
                            <strong>{{ alert.title }}</strong><br>
                            {{ alert.message }}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <p>لا توجد تنبيهات</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="section-header">
                    <h6 class="mb-0"><i class="fas fa-history"></i> الأنشطة الأخيرة</h6>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="{{ activity.icon }} text-{{ activity.color }}"></i>
                                    <strong>{{ activity.title }}</strong><br>
                                    <small class="text-muted">{{ activity.description }}</small>
                                </div>
                                <small class="text-muted">{{ activity.time|timesince }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث البيانات كل دقيقة
    setInterval(updateDashboard, 60000);
});

function updateDashboard() {
    fetch('/installations/api/dashboard-stats/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStats(data.stats);
        }
    })
    .catch(error => {
        console.error('Error updating dashboard:', error);
    });
}

function updateStats(stats) {
    // تحديث الإحصائيات السريعة
    console.log('Dashboard updated:', stats);
}
</script>
{% endblock %}
