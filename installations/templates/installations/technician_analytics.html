{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تحليل أداء الفنيين{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .analytics-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    
    .stat-box {
        text-align: center;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .performance-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .performance-excellent { background-color: #d4edda; color: #155724; }
    .performance-very-good { background-color: #d1ecf1; color: #0c5460; }
    .performance-good { background-color: #fff3cd; color: #856404; }
    .performance-needs-improvement { background-color: #f8d7da; color: #721c24; }
    
    .capacity-bar {
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }
    
    .capacity-fill {
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 10px;
    }
    
    .capacity-normal { background-color: #28a745; }
    .capacity-warning { background-color: #ffc107; }
    .capacity-danger { background-color: #dc3545; }
    
    .capacity-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.8rem;
        font-weight: bold;
        color: #333;
    }
    
    .technician-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .technician-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
    }
    
    .comparison-table {
        font-size: 0.9rem;
    }
    
    .comparison-table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
    }
    
    .rank-badge {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.8rem;
    }
    
    .rank-1 { background-color: #ffd700; color: #333; }
    .rank-2 { background-color: #c0c0c0; color: #333; }
    .rank-3 { background-color: #cd7f32; color: #fff; }
    .rank-other { background-color: #6c757d; color: #fff; }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .daily-schedule {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .installation-item {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 8px;
        border-left: 4px solid #007bff;
    }
    
    .installation-item.priority-urgent { border-left-color: #dc3545; }
    .installation-item.priority-high { border-left-color: #fd7e14; }
    .installation-item.priority-normal { border-left-color: #28a745; }
    .installation-item.priority-low { border-left-color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-chart-line"></i> تحليل أداء الفنيين</h1>
        <div>
            <button class="btn btn-primary" id="export-report-btn">
                <i class="fas fa-file-export"></i> تصدير التقرير
            </button>
            <button class="btn btn-success" id="refresh-btn">
                <i class="fas fa-sync"></i> تحديث
            </button>
        </div>
    </div>

    <!-- فلاتر -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label>الفني:</label>
                <select class="form-control" id="technician-filter">
                    <option value="">جميع الفنيين</option>
                    {% for technician in technicians %}
                    <option value="{{ technician.id }}">{{ technician.user.get_full_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>الفرع:</label>
                <select class="form-control" id="branch-filter">
                    <option value="">جميع الفروع</option>
                    {% for branch in branches %}
                    <option value="{{ branch }}">{{ branch }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label>من تاريخ:</label>
                <input type="date" class="form-control" id="start-date" value="{{ default_start_date }}">
            </div>
            <div class="col-md-2">
                <label>إلى تاريخ:</label>
                <input type="date" class="form-control" id="end-date" value="{{ default_end_date }}">
            </div>
            <div class="col-md-2">
                <label>&nbsp;</label>
                <button class="btn btn-primary btn-block" id="apply-filters-btn">
                    <i class="fas fa-filter"></i> تطبيق
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات عامة -->
    <div class="row">
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="stat-box bg-primary text-white">
                    <div class="stat-number" id="total-technicians">{{ summary.total_technicians }}</div>
                    <div class="stat-label">إجمالي الفنيين</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="stat-box bg-success text-white">
                    <div class="stat-number" id="total-windows">{{ summary.total_windows }}</div>
                    <div class="stat-label">إجمالي الشبابيك</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="stat-box bg-info text-white">
                    <div class="stat-number" id="total-installations">{{ summary.total_installations }}</div>
                    <div class="stat-label">إجمالي التركيبات</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="stat-box bg-warning text-white">
                    <div class="stat-number" id="avg-windows">{{ summary.avg_windows_per_technician|floatformat:1 }}</div>
                    <div class="stat-label">متوسط الشبابيك/فني</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تبويبات التحليل -->
    <ul class="nav nav-tabs" id="analyticsTab" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="comparison-tab" data-toggle="tab" href="#comparison" role="tab">
                <i class="fas fa-balance-scale"></i> مقارنة الأداء
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="individual-tab" data-toggle="tab" href="#individual" role="tab">
                <i class="fas fa-user"></i> تحليل فردي
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="teams-tab" data-toggle="tab" href="#teams" role="tab">
                <i class="fas fa-users"></i> تحليل الفرق
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="trends-tab" data-toggle="tab" href="#trends" role="tab">
                <i class="fas fa-chart-line"></i> الاتجاهات
            </a>
        </li>
    </ul>

    <div class="tab-content" id="analyticsTabContent">
        <!-- مقارنة الأداء -->
        <div class="tab-pane fade show active" id="comparison" role="tabpanel">
            <div class="analytics-card">
                <h5><i class="fas fa-trophy"></i> ترتيب الفنيين حسب الأداء</h5>
                
                <div class="table-responsive">
                    <table class="table comparison-table">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>الفني</th>
                                <th>الفرع</th>
                                <th>سنوات الخبرة</th>
                                <th>إجمالي الشبابيك</th>
                                <th>إجمالي التركيبات</th>
                                <th>معدل الإكمال</th>
                                <th>متوسط يومي</th>
                                <th>استغلال السعة</th>
                                <th>تقييم الجودة</th>
                                <th>الأداء</th>
                            </tr>
                        </thead>
                        <tbody id="comparison-tbody">
                            {% for technician in comparison_data %}
                            <tr>
                                <td>
                                    <span class="rank-badge rank-{% if forloop.counter <= 3 %}{{ forloop.counter }}{% else %}other{% endif %}">
                                        {{ forloop.counter }}
                                    </span>
                                </td>
                                <td>
                                    <strong>{{ technician.technician.name }}</strong><br>
                                    <small class="text-muted">{{ technician.technician.employee_id }}</small>
                                </td>
                                <td>{{ technician.technician.branch }}</td>
                                <td>{{ technician.technician.experience_years }} سنة</td>
                                <td>
                                    <span class="badge badge-success">{{ technician.performance.total_windows }}</span>
                                </td>
                                <td>{{ technician.performance.total_installations }}</td>
                                <td>
                                    <div class="capacity-bar">
                                        <div class="capacity-fill capacity-{% if technician.performance.completion_rate >= 90 %}normal{% elif technician.performance.completion_rate >= 70 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ technician.performance.completion_rate }}%"></div>
                                        <div class="capacity-text">{{ technician.performance.completion_rate|floatformat:1 }}%</div>
                                    </div>
                                </td>
                                <td>{{ technician.performance.avg_daily_windows|floatformat:1 }}</td>
                                <td>
                                    <div class="capacity-bar">
                                        <div class="capacity-fill capacity-{% if technician.performance.capacity_utilization >= 90 %}danger{% elif technician.performance.capacity_utilization >= 70 %}warning{% else %}normal{% endif %}" 
                                             style="width: {{ technician.performance.capacity_utilization }}%"></div>
                                        <div class="capacity-text">{{ technician.performance.capacity_utilization|floatformat:1 }}%</div>
                                    </div>
                                </td>
                                <td>
                                    {% if technician.performance.avg_quality_rating %}
                                        <span class="badge badge-{% if technician.performance.avg_quality_rating >= 4 %}success{% elif technician.performance.avg_quality_rating >= 3 %}warning{% else %}danger{% endif %}">
                                            {{ technician.performance.avg_quality_rating|floatformat:1 }}/5
                                        </span>
                                    {% else %}
                                        <span class="text-muted">غير متاح</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="performance-badge performance-excellent">ممتاز</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تحليل فردي -->
        <div class="tab-pane fade" id="individual" role="tabpanel">
            <div class="row">
                <div class="col-md-4">
                    <div class="analytics-card">
                        <h6>اختر فني للتحليل التفصيلي:</h6>
                        <select class="form-control" id="individual-technician-select">
                            <option value="">اختر فني...</option>
                            {% for technician in technicians %}
                            <option value="{{ technician.id }}">{{ technician.user.get_full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="analytics-card">
                        <div id="individual-analysis-content">
                            <p class="text-muted text-center">اختر فني لعرض التحليل التفصيلي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحليل الفرق -->
        <div class="tab-pane fade" id="teams" role="tabpanel">
            <div class="analytics-card">
                <h5><i class="fas fa-users"></i> أداء الفرق</h5>
                
                <div class="row">
                    {% for team in teams_data %}
                    <div class="col-md-6">
                        <div class="technician-card">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6>{{ team.name }}</h6>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-user-tie"></i> {{ team.leader_name|default:"لا يوجد قائد" }}<br>
                                        <i class="fas fa-building"></i> {{ team.branch }}<br>
                                        <i class="fas fa-users"></i> {{ team.members_count }} عضو
                                    </p>
                                </div>
                                <div class="text-right">
                                    <div class="stat-number text-primary">{{ team.total_windows }}</div>
                                    <div class="stat-label">شباك</div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="capacity-bar">
                                    <div class="capacity-fill capacity-{% if team.utilization >= 90 %}danger{% elif team.utilization >= 70 %}warning{% else %}normal{% endif %}" 
                                         style="width: {{ team.utilization }}%"></div>
                                    <div class="capacity-text">{{ team.utilization|floatformat:1 }}% استغلال</div>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="showTeamDetails({{ team.id }})">
                                    <i class="fas fa-eye"></i> تفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- الاتجاهات -->
        <div class="tab-pane fade" id="trends" role="tabpanel">
            <div class="analytics-card">
                <h5><i class="fas fa-chart-line"></i> اتجاهات الأداء</h5>
                
                <div class="chart-container">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
