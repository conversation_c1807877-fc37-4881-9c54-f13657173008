{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}التقويم الذكي للتركيبات{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet">
<style>
    .calendar-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }
    
    .fc-event {
        border: none !important;
        border-radius: 4px;
        font-size: 0.8rem;
        padding: 2px 4px;
        margin: 1px 0;
    }
    
    .fc-event-pending { background-color: #ffc107; color: #000; }
    .fc-event-ready { background-color: #17a2b8; color: #fff; }
    .fc-event-scheduled { background-color: #28a745; color: #fff; }
    .fc-event-in_progress { background-color: #007bff; color: #fff; }
    .fc-event-completed { background-color: #6c757d; color: #fff; }
    .fc-event-cancelled { background-color: #dc3545; color: #fff; }
    
    .fc-event-urgent { border-left: 4px solid #dc3545 !important; }
    .fc-event-high { border-left: 4px solid #fd7e14 !important; }
    
    .fc-daygrid-day.fc-day-today {
        background-color: #e3f2fd !important;
    }
    
    .fc-daygrid-day:hover {
        background-color: #f5f5f5;
        cursor: pointer;
    }
    
    .day-stats {
        position: absolute;
        top: 2px;
        right: 2px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border-radius: 10px;
        padding: 2px 6px;
        font-size: 0.7rem;
        font-weight: bold;
    }
    
    .overloaded-day {
        background-color: #ffebee !important;
    }
    
    .overloaded-day .day-stats {
        background: #d32f2f !important;
    }
    
    .daily-details-modal .modal-dialog {
        max-width: 90%;
    }
    
    .installation-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    
    .installation-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .installation-card.status-pending { border-left: 4px solid #ffc107; }
    .installation-card.status-ready { border-left: 4px solid #17a2b8; }
    .installation-card.status-scheduled { border-left: 4px solid #28a745; }
    .installation-card.status-in_progress { border-left: 4px solid #007bff; }
    .installation-card.status-completed { border-left: 4px solid #6c757d; }
    .installation-card.status-cancelled { border-left: 4px solid #dc3545; }
    
    .priority-badge {
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
    }
    
    .priority-urgent { background-color: #dc3545; color: white; }
    .priority-high { background-color: #fd7e14; color: white; }
    .priority-normal { background-color: #28a745; color: white; }
    .priority-low { background-color: #6c757d; color: white; }
    
    .time-slot {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 4px 8px;
        margin: 2px 0;
        font-size: 0.85rem;
    }
    
    .calendar-controls {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .legend {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 15px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    
    .stats-summary {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .stat-item {
        text-align: center;
        padding: 10px;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-calendar-alt"></i> التقويم الذكي للتركيبات</h1>
        <div>
            <button class="btn btn-primary" id="today-btn">
                <i class="fas fa-calendar-day"></i> اليوم
            </button>
            <button class="btn btn-success" id="refresh-btn">
                <i class="fas fa-sync"></i> تحديث
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-summary">
        <div class="row">
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-warning" id="pending-stat">{{ monthly_stats.pending }}</div>
                    <div class="stat-label">قيد الانتظار</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-info" id="ready-stat">{{ monthly_stats.ready }}</div>
                    <div class="stat-label">جاهز</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-success" id="scheduled-stat">{{ monthly_stats.scheduled }}</div>
                    <div class="stat-label">مجدول</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-primary" id="in-progress-stat">{{ monthly_stats.in_progress }}</div>
                    <div class="stat-label">جاري التنفيذ</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-secondary" id="completed-stat">{{ monthly_stats.completed }}</div>
                    <div class="stat-label">مكتمل</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-item">
                    <div class="stat-number text-danger" id="overloaded-stat">{{ monthly_stats.overloaded_days }}</div>
                    <div class="stat-label">أيام مزدحمة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="calendar-controls">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label>الفرع:</label>
                <select class="form-control" id="branch-filter">
                    <option value="">جميع الفروع</option>
                    {% for branch in branches %}
                    <option value="{{ branch }}" {% if branch == selected_branch %}selected{% endif %}>{{ branch }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>الفريق:</label>
                <select class="form-control" id="team-filter">
                    <option value="">جميع الفرق</option>
                    {% for team in teams %}
                    <option value="{{ team.id }}">{{ team.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>الحالة:</label>
                <select class="form-control" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="ready">جاهز</option>
                    <option value="scheduled">مجدول</option>
                    <option value="in_progress">جاري التنفيذ</option>
                    <option value="completed">مكتمل</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="show-overloaded">
                    <label class="form-check-label" for="show-overloaded">
                        إظهار الأيام المزدحمة فقط
                    </label>
                </div>
            </div>
        </div>
        
        <!-- مفتاح الألوان -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffc107;"></div>
                <span>قيد الانتظار</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #17a2b8;"></div>
                <span>جاهز</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #28a745;"></div>
                <span>مجدول</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #007bff;"></div>
                <span>جاري التنفيذ</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #6c757d;"></div>
                <span>مكتمل</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #dc3545;"></div>
                <span>ملغي</span>
            </div>
        </div>
    </div>

    <!-- التقويم -->
    <div class="calendar-container">
        <div id="calendar"></div>
    </div>
</div>

<!-- نافذة تفاصيل اليوم -->
<div class="modal fade daily-details-modal" id="dailyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-day"></i>
                    تفاصيل يوم <span id="selected-date"></span>
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>إجمالي التركيبات: </strong>
                                <span id="daily-total" class="badge badge-primary">0</span>
                                <strong class="ml-3">إجمالي الشبابيك: </strong>
                                <span id="daily-windows" class="badge badge-info">0</span>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success" id="print-daily-btn">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                                <button class="btn btn-sm btn-primary" id="export-daily-btn">
                                    <i class="fas fa-file-pdf"></i> تصدير PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <h6>قائمة التركيبات:</h6>
                        <div id="daily-installations-list">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>إحصائيات اليوم:</h6>
                        <div id="daily-stats">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                        
                        <h6 class="mt-4">الفرق العاملة:</h6>
                        <div id="daily-teams">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="schedule-new-btn">
                    <i class="fas fa-plus"></i> جدولة تركيب جديد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/locales/ar.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('calendar');
    let calendar;
    let currentEvents = [];

    // إعداد التقويم
    calendar = new FullCalendar.Calendar(calendarEl, {
        locale: 'ar',
        direction: 'rtl',
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,dayGridWeek,listWeek'
        },
        height: 'auto',
        events: function(fetchInfo, successCallback, failureCallback) {
            loadCalendarEvents(fetchInfo.startStr, fetchInfo.endStr, successCallback, failureCallback);
        },
        eventClick: function(info) {
            info.jsEvent.preventDefault();
            showInstallationDetails(info.event.extendedProps.installation);
        },
        dateClick: function(info) {
            showDayDetails(info.dateStr);
        },
        dayCellDidMount: function(info) {
            addDayStats(info);
        },
        eventDidMount: function(info) {
            customizeEvent(info);
        }
    });

    calendar.render();

    // تحميل أحداث التقويم
    function loadCalendarEvents(start, end, successCallback, failureCallback) {
        const filters = {
            start: start,
            end: end,
            branch: $('#branch-filter').val(),
            team: $('#team-filter').val(),
            status: $('#status-filter').val(),
            show_overloaded: $('#show-overloaded').is(':checked')
        };

        $.ajax({
            url: '/installations/api/calendar-events/',
            method: 'GET',
            data: filters,
            success: function(response) {
                currentEvents = response.events;
                successCallback(response.events);
                updateMonthlyStats(response.stats);
            },
            error: function(xhr) {
                failureCallback(xhr);
                showAlert('خطأ في تحميل بيانات التقويم', 'error');
            }
        });
    }

    // عرض تفاصيل اليوم
    function showDayDetails(dateStr) {
        const selectedDate = new Date(dateStr);
        const formattedDate = selectedDate.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        $('#selected-date').text(formattedDate);

        $.ajax({
            url: '/installations/api/daily-details/',
            method: 'GET',
            data: {
                date: dateStr,
                branch: $('#branch-filter').val(),
                team: $('#team-filter').val()
            },
            success: function(response) {
                populateDayDetails(response);
                $('#dailyDetailsModal').modal('show');
            },
            error: function(xhr) {
                showAlert('خطأ في تحميل تفاصيل اليوم', 'error');
            }
        });
    }

    // إضافة إحصائيات اليوم
    function addDayStats(info) {
        const dateStr = info.date.toISOString().split('T')[0];
        const dayEvents = currentEvents.filter(event =>
            event.start.startsWith(dateStr)
        );

        if (dayEvents.length > 0) {
            const statsEl = document.createElement('div');
            statsEl.className = 'day-stats';
            statsEl.textContent = dayEvents.length;

            if (dayEvents.length >= 13) {
                statsEl.style.backgroundColor = '#d32f2f';
                info.el.classList.add('overloaded-day');
            } else if (dayEvents.length >= 10) {
                statsEl.style.backgroundColor = '#f57c00';
            } else {
                statsEl.style.backgroundColor = '#388e3c';
            }

            info.el.appendChild(statsEl);
        }
    }

    // تخصيص مظهر الأحداث
    function customizeEvent(info) {
        const installation = info.event.extendedProps.installation;
        info.el.classList.add(`fc-event-${installation.status}`);

        if (installation.priority === 'urgent' || installation.priority === 'high') {
            info.el.classList.add(`fc-event-${installation.priority}`);
        }

        info.el.title = `${installation.customer_name} - ${installation.windows_count} شباك`;
    }

    // ملء تفاصيل اليوم
    function populateDayDetails(data) {
        $('#daily-total').text(data.statistics.total_installations);
        $('#daily-windows').text(data.statistics.total_windows);

        const installationsList = $('#daily-installations-list');
        installationsList.empty();

        if (data.installations_list.length === 0) {
            installationsList.html('<p class="text-muted">لا توجد تركيبات مجدولة في هذا اليوم</p>');
        } else {
            data.installations_list.forEach(installation => {
                const card = createInstallationCard(installation);
                installationsList.append(card);
            });
        }
    }

    // إنشاء بطاقة تركيب
    function createInstallationCard(installation) {
        return $(`
            <div class="installation-card status-${installation.status}">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>${installation.customer_name}</h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone"></i> ${installation.customer_phone}
                            <span class="ml-3">
                                <i class="fas fa-window-maximize"></i> ${installation.windows_count} شباك
                            </span>
                        </p>
                        ${installation.scheduled_time_start ? `
                            <div class="time-slot">
                                <i class="fas fa-clock"></i>
                                ${installation.scheduled_time_start} - ${installation.scheduled_time_end || 'غير محدد'}
                            </div>
                        ` : ''}
                    </div>
                    <div>
                        <span class="badge badge-primary">${installation.status}</span>
                    </div>
                </div>
            </div>
        `);
    }

    // معالجات الأحداث
    $('#branch-filter, #team-filter, #status-filter').on('change', function() {
        calendar.refetchEvents();
    });

    $('#show-overloaded').on('change', function() {
        calendar.refetchEvents();
    });

    $('#today-btn').on('click', function() {
        calendar.today();
    });

    $('#refresh-btn').on('click', function() {
        calendar.refetchEvents();
    });

    function showAlert(message, type) {
        // تنفيذ عرض التنبيه
        console.log(message);
    }
});
</script>
{% endblock %}
