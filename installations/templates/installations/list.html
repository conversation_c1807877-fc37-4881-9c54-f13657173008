{% extends "base.html" %}
{% load i18n %}

{% block title %}قائمة التركيبات{% endblock %}

{% block extra_css %}
<style>
    .installation-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
        margin-bottom: 15px;
    }

    .installation-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 600;
    }

    .status-pending { background: #fff3cd; color: #856404; }
    .status-scheduled { background: #d1ecf1; color: #0c5460; }
    .status-in_progress { background: #cce5ff; color: #004085; }
    .status-completed { background: #d4edda; color: #155724; }
    .status-cancelled { background: #f8d7da; color: #721c24; }

    .priority-urgent { border-left: 4px solid #dc3545; }
    .priority-high { border-left: 4px solid #fd7e14; }
    .priority-normal { border-left: 4px solid #28a745; }
    .priority-low { border-left: 4px solid #6c757d; }

    .filter-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-list text-primary"></i> قائمة التركيبات
            </h1>
            <p class="text-muted mb-0">إدارة وعرض جميع التركيبات</p>
        </div>
        <div>
            <a href="{% url 'installations_new:create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> تركيب جديد
            </a>
            <a href="{% url 'installations_new:dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-home"></i> لوحة التحكم
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search"
                       value="{{ request.GET.search }}" placeholder="اسم العميل أو رقم الهاتف">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>معلق</option>
                    <option value="scheduled" {% if request.GET.status == 'scheduled' %}selected{% endif %}>مجدول</option>
                    <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتمل</option>
                    <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الأولوية</label>
                <select class="form-select" name="priority">
                    <option value="">جميع الأولويات</option>
                    <option value="urgent" {% if request.GET.priority == 'urgent' %}selected{% endif %}>عاجل</option>
                    <option value="high" {% if request.GET.priority == 'high' %}selected{% endif %}>عالي</option>
                    <option value="normal" {% if request.GET.priority == 'normal' %}selected{% endif %}>عادي</option>
                    <option value="low" {% if request.GET.priority == 'low' %}selected{% endif %}>منخفض</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">التاريخ من</label>
                <input type="date" class="form-control" name="date_from" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">التاريخ إلى</label>
                <input type="date" class="form-control" name="date_to" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-primary">{{ total_count|default:0 }}</h5>
                    <p class="mb-0">إجمالي التركيبات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-warning">{{ pending_count|default:0 }}</h5>
                    <p class="mb-0">معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-info">{{ scheduled_count|default:0 }}</h5>
                    <p class="mb-0">مجدولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-success">{{ completed_count|default:0 }}</h5>
                    <p class="mb-0">مكتملة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Installations List -->
    <div class="row">
        {% for installation in installations %}
        <div class="col-lg-6 col-xl-4">
            <div class="installation-card card priority-{{ installation.priority }}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">{{ installation.customer_name }}</h6>
                        <span class="status-badge status-{{ installation.status }}">
                            {{ installation.get_status_display }}
                        </span>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-phone"></i> {{ installation.customer_phone }}
                        </small>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-window-maximize"></i> {{ installation.windows_count }} شباك
                        </small>
                        {% if installation.team %}
                        <small class="text-muted ms-3">
                            <i class="fas fa-users"></i> {{ installation.team.name }}
                        </small>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> {{ installation.scheduled_date|date:"Y-m-d" }}
                        </small>
                        {% if installation.priority != 'normal' %}
                        <span class="badge bg-{{ installation.priority|yesno:'danger,warning,success' }} ms-2">
                            {{ installation.get_priority_display }}
                        </span>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <a href="{% url 'installations_new:detail' installation.id %}"
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <a href="{% url 'installations_new:edit' installation.id %}"
                           class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        {% if installation.status == 'scheduled' %}
                        <button class="btn btn-sm btn-outline-success"
                                onclick="startInstallation({{ installation.id }})">
                            <i class="fas fa-play"></i> بدء
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تركيبات</h5>
                <p class="text-muted">لم يتم العثور على تركيبات تطابق معايير البحث</p>
                <a href="{% url 'installations_new:create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة تركيب جديد
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="صفحات التركيبات">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{{ request.GET.urlencode }}">الأولى</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{{ request.GET.urlencode }}">السابقة</a>
            </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{{ request.GET.urlencode }}">التالية</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{{ request.GET.urlencode }}">الأخيرة</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function startInstallation(installationId) {
    if (confirm('هل تريد بدء هذا التركيب؟')) {
        fetch(`/installations/api/start-installation/${installationId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
