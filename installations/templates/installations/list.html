{% extends 'base.html' %}

{% block title %}قائمة التركيبات - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">قائمة التركيبات</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'installations_new:create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة تركيب جديد
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search"
                                   value="{{ request.GET.search }}" placeholder="البحث في التركيبات...">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="in_production" {% if request.GET.status == 'in_production' %}selected{% endif %}>قيد الإنتاج</option>
                            <option value="ready" {% if request.GET.status == 'ready' %}selected{% endif %}>جاهز للتركيب</option>
                            <option value="scheduled" {% if request.GET.status == 'scheduled' %}selected{% endif %}>مجدول</option>
                            <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتمل</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">الأولوية</label>
                        <select class="form-select" name="priority">
                            <option value="">جميع الأولويات</option>
                            <option value="urgent" {% if request.GET.priority == 'urgent' %}selected{% endif %}>عاجل</option>
                            <option value="high" {% if request.GET.priority == 'high' %}selected{% endif %}>عالي</option>
                            <option value="normal" {% if request.GET.priority == 'normal' %}selected{% endif %}>عادي</option>
                            <option value="low" {% if request.GET.priority == 'low' %}selected{% endif %}>منخفض</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">التاريخ من</label>
                        <input type="date" class="form-control" name="date_from" value="{{ request.GET.date_from }}">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">التاريخ إلى</label>
                        <input type="date" class="form-control" name="date_to" value="{{ request.GET.date_to }}">
                    </div>
                    <div class="col-md-12 mb-2 d-flex align-items-end">
                        <a href="{% url 'installations_new:list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Installations Table -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--secondary); color: white;">
            <h5 class="mb-0">التركيبات ({{ installations|length }})</h5>
        </div>
        <div class="card-body p-0">
            {% if installations %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background-color: var(--light-accent); color: var(--dark-text);">
                        <tr>
                            <th>رقم التركيب</th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>عدد الشبابيك</th>
                            <th>تاريخ التركيب</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>الفريق</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for installation in installations %}
                        <tr style="cursor: pointer;" onclick="window.location.href='{% url 'installations_new:detail' installation.id %}'">
                            <td><strong>#{{ installation.id }}</strong></td>
                            <td>{{ installation.customer_name }}</td>
                            <td>{{ installation.customer_phone }}</td>
                            <td>
                                <span class="badge bg-info">{{ installation.windows_count }}</span>
                            </td>
                            <td>{{ installation.scheduled_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if installation.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif installation.status == 'in_production' %}
                                    <span class="badge bg-primary">قيد الإنتاج</span>
                                {% elif installation.status == 'ready' %}
                                    <span class="badge bg-success">جاهز للتركيب</span>
                                {% elif installation.status == 'scheduled' %}
                                    <span class="badge bg-info">مجدول</span>
                                {% elif installation.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                {% elif installation.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ installation.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if installation.priority == 'urgent' %}
                                    <span class="badge bg-danger">عاجل</span>
                                {% elif installation.priority == 'high' %}
                                    <span class="badge bg-warning">عالي</span>
                                {% elif installation.priority == 'low' %}
                                    <span class="badge bg-secondary">منخفض</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">عادي</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if installation.team %}
                                    {{ installation.team.name }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td onclick="event.stopPropagation();">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'installations_new:detail' installation.id %}"
                                       class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'installations_new:edit' installation.id %}"
                                       class="btn btn-sm btn-outline-secondary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if installation.status == 'ready' %}
                                    <button class="btn btn-sm btn-outline-success"
                                            onclick="scheduleInstallation({{ installation.id }})" title="جدولة">
                                        <i class="fas fa-calendar-plus"></i>
                                    </button>
                                    {% elif installation.status == 'scheduled' %}
                                    <button class="btn btn-sm btn-outline-info"
                                            onclick="startInstallation({{ installation.id }})" title="بدء">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تركيبات</h5>
                <p class="text-muted">لم يتم العثور على تركيبات تطابق معايير البحث</p>
                <a href="{% url 'installations_new:create' %}" class="btn" style="background-color: var(--primary); color: white;">
                    <i class="fas fa-plus"></i> إضافة تركيب جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="صفحات التركيبات">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{{ request.GET.urlencode }}">الأولى</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{{ request.GET.urlencode }}">السابقة</a>
            </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{{ request.GET.urlencode }}">التالية</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{{ request.GET.urlencode }}">الأخيرة</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function startInstallation(installationId) {
    if (confirm('هل تريد بدء هذا التركيب؟')) {
        fetch(`/installations/api/start-installation/${installationId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
