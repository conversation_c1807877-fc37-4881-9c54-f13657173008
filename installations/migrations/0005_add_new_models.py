# Generated by Django 4.2.21 on 2025-07-03 09:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('installations', '0004_dailyinstallationreport_installationtechnician_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='installationqualitycheck',
            options={'verbose_name': 'فحص جودة تركيب', 'verbose_name_plural': 'فحوصات جودة التركيب'},
        ),
        migrations.RenameField(
            model_name='installationqualitycheck',
            old_name='created_at',
            new_name='checked_at',
        ),
        migrations.AlterUniqueTogether(
            name='installationqualitycheck',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='alignment_score',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='درجة المحاذاة'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='cleanliness_score',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='درجة النظافة'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='defects_found',
            field=models.TextField(blank=True, verbose_name='عيوب تم اكتشافها'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='finish_score',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='درجة التشطيب'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='functionality_score',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='درجة الوظائف'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='improvement_notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات للتحسين'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='is_approved',
            field=models.BooleanField(default=False, verbose_name='معتمد'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='overall_rating',
            field=models.DecimalField(decimal_places=2, default=5.0, max_digits=3, verbose_name='التقييم الإجمالي'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='positive_notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات إيجابية'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='requires_rework',
            field=models.BooleanField(default=False, verbose_name='يتطلب إعادة عمل'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='safety_score',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='درجة السلامة'),
        ),
        migrations.AlterField(
            model_name='installationqualitycheck',
            name='checked_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='فحص بواسطة'),
        ),
        migrations.AlterField(
            model_name='installationqualitycheck',
            name='installation',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='quality_check', to='installations.installationnew', verbose_name='التركيب'),
        ),
        migrations.CreateModel(
            name='InstallationPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo', models.ImageField(upload_to='installations/photos/', verbose_name='الصورة')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='وصف الصورة')),
                ('photo_type', models.CharField(choices=[('before', 'قبل التركيب'), ('during', 'أثناء التركيب'), ('after', 'بعد التركيب'), ('problem', 'مشكلة'), ('solution', 'حل')], default='during', max_length=20, verbose_name='نوع الصورة')),
                ('taken_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقاط')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='installations.installationnew', verbose_name='التركيب')),
                ('taken_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التقاط بواسطة')),
            ],
            options={
                'verbose_name': 'صورة تركيب',
                'verbose_name_plural': 'صور التركيب',
                'ordering': ['-taken_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_type', models.CharField(choices=[('general', 'عامة'), ('technical', 'فنية'), ('customer', 'خاصة بالعميل'), ('quality', 'جودة'), ('safety', 'سلامة'), ('delay', 'تأخير')], default='general', max_length=20, verbose_name='نوع الملاحظة')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='تم الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='installation_notes', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='installations.installationnew', verbose_name='التركيب')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_installation_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
            ],
            options={
                'verbose_name': 'ملاحظة تركيب',
                'verbose_name_plural': 'ملاحظات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('material_name', models.CharField(max_length=200, verbose_name='اسم المادة')),
                ('material_code', models.CharField(blank=True, max_length=50, verbose_name='كود المادة')),
                ('quantity_required', models.PositiveIntegerField(verbose_name='الكمية المطلوبة')),
                ('quantity_used', models.PositiveIntegerField(default=0, verbose_name='الكمية المستخدمة')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='التكلفة الإجمالية')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='المورد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='installations.installationnew', verbose_name='التركيب')),
            ],
            options={
                'verbose_name': 'مادة تركيب',
                'verbose_name_plural': 'مواد التركيب',
                'ordering': ['material_name'],
            },
        ),
        migrations.CreateModel(
            name='InstallationCustomerFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_satisfaction', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='الرضا العام')),
                ('quality_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='تقييم الجودة')),
                ('timeliness_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='تقييم الالتزام بالوقت')),
                ('professionalism_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='تقييم الاحترافية')),
                ('cleanliness_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='تقييم النظافة')),
                ('positive_feedback', models.TextField(blank=True, verbose_name='التعليقات الإيجابية')),
                ('negative_feedback', models.TextField(blank=True, verbose_name='التعليقات السلبية')),
                ('suggestions', models.TextField(blank=True, verbose_name='اقتراحات للتحسين')),
                ('would_recommend', models.BooleanField(default=True, verbose_name='يوصي بالخدمة')),
                ('would_use_again', models.BooleanField(default=True, verbose_name='سيستخدم الخدمة مرة أخرى')),
                ('feedback_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('feedback_method', models.CharField(choices=[('phone', 'هاتف'), ('email', 'بريد إلكتروني'), ('sms', 'رسالة نصية'), ('in_person', 'شخصياً'), ('online', 'عبر الإنترنت')], default='phone', max_length=20, verbose_name='طريقة التقييم')),
                ('installation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customer_feedback', to='installations.installationnew', verbose_name='التركيب')),
            ],
            options={
                'verbose_name': 'تقييم عميل للتركيب',
                'verbose_name_plural': 'تقييمات العملاء للتركيبات',
            },
        ),
        migrations.RemoveField(
            model_name='installationqualitycheck',
            name='criteria',
        ),
        migrations.RemoveField(
            model_name='installationqualitycheck',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='installationqualitycheck',
            name='photo',
        ),
        migrations.RemoveField(
            model_name='installationqualitycheck',
            name='rating',
        ),
        migrations.CreateModel(
            name='InstallationQualityCheckOld',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('criteria', models.CharField(choices=[('alignment', 'المحاذاة'), ('finishing', 'التشطيب'), ('functionality', 'الوظائف'), ('safety', 'السلامة'), ('cleanliness', 'النظافة')], max_length=20, verbose_name='معيار التقييم')),
                ('rating', models.IntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='التقييم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='installations/quality/', verbose_name='صورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الفحص')),
                ('checked_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='quality_checks', to=settings.AUTH_USER_MODEL, verbose_name='تم الفحص بواسطة')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='installations.installation', verbose_name='عملية التركيب')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['-created_at'],
                'unique_together': {('installation', 'criteria')},
            },
        ),
    ]
