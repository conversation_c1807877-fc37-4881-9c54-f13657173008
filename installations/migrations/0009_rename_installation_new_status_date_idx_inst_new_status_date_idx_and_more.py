# Generated by Django 4.2.21 on 2025-07-03 11:14

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('installations', '0008_make_order_optional'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_status_date_idx',
            old_name='installation_new_status_date_idx',
        ),
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_customer_idx',
            old_name='installation_new_customer_idx',
        ),
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_branch_status_idx',
            old_name='installation_new_branch_status_idx',
        ),
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_ready_idx',
            old_name='installation_new_ready_idx',
        ),
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_payment_idx',
            old_name='installation_new_payment_idx',
        ),
        migrations.RenameIndex(
            model_name='installationnew',
            new_name='inst_new_team_date_idx',
            old_name='installation_new_team_date_idx',
        ),
    ]
