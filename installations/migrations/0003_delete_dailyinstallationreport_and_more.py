# Generated by Django 4.2.21 on 2025-07-03 08:51

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('installations', '0002_dailyinstallationreport_installationtechnician_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='DailyInstallationReport',
        ),
        migrations.RemoveField(
            model_name='installationcompletionlog',
            name='completed_by',
        ),
        migrations.RemoveField(
            model_name='installationcompletionlog',
            name='installation',
        ),
        migrations.RemoveField(
            model_name='installationnew',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='installationnew',
            name='inspection',
        ),
        migrations.RemoveField(
            model_name='installationnew',
            name='order',
        ),
        migrations.RemoveField(
            model_name='installationnew',
            name='team',
        ),
        migrations.RemoveField(
            model_name='installationnew',
            name='updated_by',
        ),
        migrations.AlterUniqueTogether(
            name='installationschedule',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='installationschedule',
            name='installation',
        ),
        migrations.RemoveField(
            model_name='installationteamnew',
            name='branch',
        ),
        migrations.RemoveField(
            model_name='installationteamnew',
            name='leader',
        ),
        migrations.RemoveField(
            model_name='installationteamnew',
            name='members',
        ),
        migrations.RemoveField(
            model_name='installationtechnician',
            name='branch',
        ),
        migrations.RemoveField(
            model_name='installationtechnician',
            name='user',
        ),
        migrations.DeleteModel(
            name='InstallationAlert',
        ),
        migrations.DeleteModel(
            name='InstallationCompletionLog',
        ),
        migrations.DeleteModel(
            name='InstallationNew',
        ),
        migrations.DeleteModel(
            name='InstallationSchedule',
        ),
        migrations.DeleteModel(
            name='InstallationTeamNew',
        ),
        migrations.DeleteModel(
            name='InstallationTechnician',
        ),
    ]
