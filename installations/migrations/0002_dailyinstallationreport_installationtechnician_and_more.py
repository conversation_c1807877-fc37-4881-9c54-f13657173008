# Generated by Django 4.2.21 on 2025-07-03 08:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inspections', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
        ('installations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyInstallationReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True, unique=True, verbose_name='التاريخ')),
                ('total_scheduled', models.PositiveIntegerField(default=0, verbose_name='إجمالي المجدول')),
                ('total_completed', models.PositiveIntegerField(default=0, verbose_name='إجمالي المكتمل')),
                ('total_cancelled', models.PositiveIntegerField(default=0, verbose_name='إجمالي الملغي')),
                ('total_windows', models.PositiveIntegerField(default=0, verbose_name='إجمالي الشبابيك')),
                ('average_quality_rating', models.FloatField(blank=True, null=True, verbose_name='متوسط تقييم الجودة')),
                ('average_customer_satisfaction', models.FloatField(blank=True, null=True, verbose_name='متوسط رضا العملاء')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تقرير يومي للتركيبات',
                'verbose_name_plural': 'التقارير اليومية للتركيبات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='InstallationTechnician',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('specializations', models.JSONField(default=list, verbose_name='التخصصات')),
                ('experience_years', models.PositiveIntegerField(default=0, verbose_name='سنوات الخبرة')),
                ('max_windows_per_day', models.PositiveIntegerField(default=20, verbose_name='الحد الأقصى للشبابيك يومياً')),
                ('hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='الأجر بالساعة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='technicians', to='accounts.branch', verbose_name='الفرع')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='technician_profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'فني تركيب',
                'verbose_name_plural': 'فنيو التركيب',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='InstallationTeamNew',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفريق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('max_daily_installations', models.PositiveIntegerField(default=5, verbose_name='الحد الأقصى للتركيبات اليومية')),
                ('specializations', models.JSONField(blank=True, default=list, verbose_name='التخصصات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installation_teams_new', to='accounts.branch', verbose_name='الفرع')),
                ('leader', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_teams_new', to=settings.AUTH_USER_MODEL, verbose_name='قائد الفريق')),
                ('members', models.ManyToManyField(related_name='installation_teams_new', to=settings.AUTH_USER_MODEL, verbose_name='أعضاء الفريق')),
            ],
            options={
                'verbose_name': 'فريق تركيب محسن',
                'verbose_name_plural': 'فرق التركيب المحسنة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InstallationNew',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(db_index=True, max_length=200, verbose_name='اسم العميل')),
                ('customer_phone', models.CharField(db_index=True, max_length=20, verbose_name='رقم هاتف العميل')),
                ('customer_phone2', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف الثاني')),
                ('customer_address', models.TextField(verbose_name='عنوان العميل')),
                ('salesperson_name', models.CharField(blank=True, max_length=200, verbose_name='اسم البائع')),
                ('branch_name', models.CharField(db_index=True, max_length=200, verbose_name='اسم الفرع')),
                ('windows_count', models.PositiveIntegerField(default=0, verbose_name='عدد الشبابيك')),
                ('location_type', models.CharField(choices=[('open', 'مكان مفتوح'), ('compound', 'كومبوند'), ('apartment', 'شقة'), ('villa', 'فيلا'), ('office', 'مكتب'), ('shop', 'محل تجاري'), ('warehouse', 'مستودع')], default='open', max_length=20, verbose_name='نوع المكان')),
                ('floor_number', models.PositiveIntegerField(blank=True, null=True, verbose_name='رقم الطابق')),
                ('order_date', models.DateTimeField(verbose_name='تاريخ الطلب')),
                ('scheduled_date', models.DateField(blank=True, db_index=True, null=True, verbose_name='تاريخ التركيب المجدول')),
                ('scheduled_time_start', models.TimeField(blank=True, null=True, verbose_name='وقت البدء المجدول')),
                ('scheduled_time_end', models.TimeField(blank=True, null=True, verbose_name='وقت الانتهاء المجدول')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء التركيب الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء التركيب الفعلي')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('ready', 'جاهز للتركيب'), ('scheduled', 'مجدول'), ('in_progress', 'جاري التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('on_hold', 'معلق'), ('rescheduled', 'تم إعادة الجدولة')], db_index=True, default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجل')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('is_ready_for_installation', models.BooleanField(db_index=True, default=False, verbose_name='جاهز للتركيب من المصنع')),
                ('payment_status', models.CharField(choices=[('pending', 'لم يتم السداد'), ('partial', 'سداد جزئي'), ('paid', 'تم السداد'), ('overdue', 'متأخر السداد')], default='pending', max_length=20, verbose_name='حالة السداد')),
                ('quality_rating', models.IntegerField(blank=True, choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], null=True, verbose_name='تقييم الجودة')),
                ('customer_satisfaction', models.IntegerField(blank=True, choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], null=True, verbose_name='رضا العميل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('special_instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('access_instructions', models.TextField(blank=True, verbose_name='تعليمات الوصول')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_installations_new', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('inspection', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='installations_new', to='inspections.inspection', verbose_name='المعاينة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installations_new', to='orders.order', verbose_name='الطلب')),
                ('team', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='installations_new', to='installations.installationteamnew', verbose_name='فريق التركيب')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_installations_new', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'عملية تركيب محسنة',
                'verbose_name_plural': 'عمليات التركيب المحسنة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationCompletionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('completion_date', models.DateTimeField(verbose_name='تاريخ الإكمال')),
                ('quality_rating', models.IntegerField(blank=True, null=True, verbose_name='تقييم الجودة')),
                ('customer_satisfaction', models.IntegerField(blank=True, null=True, verbose_name='رضا العميل')),
                ('completion_notes', models.TextField(blank=True, verbose_name='ملاحظات الإكمال')),
                ('duration_hours', models.FloatField(blank=True, null=True, verbose_name='مدة التركيب بالساعات')),
                ('issues_encountered', models.TextField(blank=True, verbose_name='المشاكل المواجهة')),
                ('customer_feedback', models.TextField(blank=True, verbose_name='تعليقات العميل')),
                ('photos_taken', models.BooleanField(default=False, verbose_name='تم التصوير')),
                ('warranty_explained', models.BooleanField(default=False, verbose_name='تم شرح الضمان')),
                ('cleanup_completed', models.BooleanField(default=False, verbose_name='تم التنظيف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('completed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_installations_log', to=settings.AUTH_USER_MODEL, verbose_name='تم الإكمال بواسطة')),
                ('installation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='completion_log', to='installations.installationnew', verbose_name='التركيب')),
            ],
            options={
                'verbose_name': 'سجل إكمال تركيب',
                'verbose_name_plural': 'سجلات إكمال التركيبات',
                'ordering': ['-completion_date'],
            },
        ),
        migrations.CreateModel(
            name='InstallationAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('overdue', 'تأخير'), ('capacity_exceeded', 'تجاوز السعة'), ('quality_issue', 'مشكلة جودة'), ('customer_complaint', 'شكوى عميل'), ('schedule_conflict', 'تعارض في الجدولة'), ('payment_overdue', 'تأخر في السداد')], max_length=20, verbose_name='نوع التنبيه')),
                ('severity', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('critical', 'حرج')], default='medium', max_length=10, verbose_name='الخطورة')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='تم الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='installations.installationnew', verbose_name='التركيب')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
            ],
            options={
                'verbose_name': 'تنبيه تركيب',
                'verbose_name_plural': 'تنبيهات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True, verbose_name='التاريخ')),
                ('time_slot_start', models.TimeField(verbose_name='بداية الفترة الزمنية')),
                ('time_slot_end', models.TimeField(verbose_name='نهاية الفترة الزمنية')),
                ('estimated_duration', models.DurationField(blank=True, null=True, verbose_name='المدة المقدرة')),
                ('buffer_time', models.DurationField(blank=True, null=True, verbose_name='وقت إضافي')),
                ('is_confirmed', models.BooleanField(default=False, verbose_name='مؤكد')),
                ('auto_scheduled', models.BooleanField(default=False, verbose_name='جدولة تلقائية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('installation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='installations.installationnew', verbose_name='التركيب')),
            ],
            options={
                'verbose_name': 'جدولة تركيب',
                'verbose_name_plural': 'جدولة التركيبات',
                'ordering': ['date', 'time_slot_start'],
                'unique_together': {('installation', 'date')},
            },
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['status', 'scheduled_date'], name='installation_new_status_date_idx'),
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['customer_name'], name='installation_new_customer_idx'),
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['branch_name', 'status'], name='installation_new_branch_status_idx'),
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['is_ready_for_installation'], name='installation_new_ready_idx'),
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['payment_status'], name='installation_new_payment_idx'),
        ),
        migrations.AddIndex(
            model_name='installationnew',
            index=models.Index(fields=['team', 'scheduled_date'], name='installation_new_team_date_idx'),
        ),
        migrations.AddIndex(
            model_name='installationalert',
            index=models.Index(fields=['alert_type', 'is_resolved'], name='alert_type_resolved_idx'),
        ),
        migrations.AddIndex(
            model_name='installationalert',
            index=models.Index(fields=['severity', 'created_at'], name='alert_severity_date_idx'),
        ),
    ]
