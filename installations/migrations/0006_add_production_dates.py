# Generated by Django 4.2.21 on 2025-07-03 09:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('installations', '0005_add_new_models'),
    ]

    operations = [
        migrations.AddField(
            model_name='installationnew',
            name='production_completed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ اكتمال الإنتاج'),
        ),
        migrations.AddField(
            model_name='installationnew',
            name='production_started_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء الإنتاج'),
        ),
        migrations.AlterField(
            model_name='installationnew',
            name='status',
            field=models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_production', 'قيد الإنتاج'), ('ready', 'جاهز للتركيب'), ('scheduled', 'مجدول'), ('in_progress', 'جاري التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('on_hold', 'معلق'), ('rescheduled', 'تم إعادة الجدولة')], db_index=True, default='pending', max_length=20, verbose_name='الحالة'),
        ),
    ]
