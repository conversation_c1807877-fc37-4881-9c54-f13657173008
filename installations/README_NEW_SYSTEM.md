# نظام إدارة التركيبات المتقدم

## نظرة عامة

نظام إدارة التركيبات المتقدم هو نظام شامل ومتطور لإدارة تركيبات الشبابيك والأبواب. يوفر النظام مجموعة كاملة من الأدوات لإدارة العمليات من البداية حتى النهاية مع تحليلات متقدمة ونظام إنذارات ذكي.

## الميزات الرئيسية

### 1. إدارة التركيبات الشاملة
- **إنشاء وتعديل التركيبات**: واجهة سهلة لإدارة جميع بيانات التركيب
- **تتبع الحالات**: نظام متقدم لتتبع حالة كل تركيب من البداية للنهاية
- **إدارة الأولويات**: تصنيف التركيبات حسب الأولوية (عاجل، عالي، عادي، منخفض)
- **ربط مع الطلبات**: ربط مباشر مع نظام الطلبات الأساسي

### 2. الجدولة الذكية
- **التقويم التفاعلي**: عرض تقويم شامل لجميع التركيبات المجدولة
- **الجدولة الديناميكية**: إمكانية تعديل المواعيد بسهولة
- **فحص التعارضات**: تنبيهات تلقائية عند وجود تعارض في المواعيد
- **توزيع الأحمال**: توزيع ذكي للتركيبات على الفرق المتاحة

### 3. إدارة الفرق والفنيين
- **إدارة الفرق**: تنظيم الفنيين في فرق عمل متخصصة
- **تتبع الأداء**: مراقبة أداء كل فني وفريق
- **إدارة السعة**: تحديد الحد الأقصى للتركيبات لكل فني/فريق
- **التخصصات**: تحديد تخصصات كل فني (نوافذ، أبواب، إلخ)

### 4. نظام الإنذارات الذكي
- **إنذارات السعة**: تنبيهات عند تجاوز الحد الأقصى للتركيبات اليومية
- **إنذارات التأخير**: تنبيهات للتركيبات المتأخرة
- **إنذارات الجودة**: تنبيهات عند انخفاض تقييمات الجودة
- **إنذارات عاجلة**: تنبيهات فورية للحالات الحرجة

### 5. التحليلات والتقارير
- **لوحة تحكم شاملة**: عرض جميع الإحصائيات الهامة في مكان واحد
- **تحليل الأداء**: تحليل مفصل لأداء الفرق والفنيين
- **مقارنة الفروع**: مقارنة أداء الفروع المختلفة
- **التحليلات التنبؤية**: توقعات للأحمال المستقبلية

### 6. التصدير والطباعة
- **تصدير PDF**: تصدير التقارير والجداول بصيغة PDF
- **تصدير Excel**: تصدير البيانات بصيغة Excel للتحليل
- **طباعة الجداول**: طباعة جداول التركيب اليومية
- **تقارير مخصصة**: إنشاء تقارير مخصصة حسب الحاجة

### 7. واجهة المصنع
- **تحديث حالة الإنتاج**: واجهة خاصة للمصنع لتحديث حالة الطلبات
- **إدارة الأولويات**: تحديد أولويات الإنتاج
- **تتبع التقدم**: مراقبة تقدم الإنتاج في الوقت الفعلي
- **إشعارات الجاهزية**: إشعارات تلقائية عند جاهزية الطلبات

## هيكل النظام

### النماذج (Models)
- `InstallationNew`: النموذج الرئيسي للتركيبات
- `InstallationTeamNew`: نموذج فرق التركيب
- `InstallationTechnician`: نموذج الفنيين
- `InstallationAlert`: نموذج الإنذارات
- `DailyInstallationReport`: نموذج التقارير اليومية
- `InstallationCompletionLog`: نموذج سجل الإكمال

### الخدمات (Services)
- `CalendarService`: خدمة إدارة التقويم والجدولة
- `AlertSystem`: نظام الإنذارات الذكي
- `TechnicianAnalyticsService`: خدمة تحليل أداء الفنيين
- `AnalyticsEngine`: محرك التحليلات الشامل
- `OrderCompletionService`: خدمة إكمال الطلبات
- `PDFExportService`: خدمة تصدير PDF
- `PrintService`: خدمة الطباعة

### العروض (Views)
- `views_new.py`: العروض الرئيسية للنظام
- `views_quick_edit.py`: عروض التعديل السريع
- `views_export.py`: عروض التصدير والطباعة
- `views_completion.py`: عروض إكمال التركيبات

## التثبيت والإعداد

### المتطلبات
```
Django >= 4.0
Python >= 3.8
PostgreSQL (مفضل) أو MySQL
Redis (للتخزين المؤقت)
Celery (للمهام المجدولة)
```

### المكتبات المطلوبة
```bash
pip install reportlab  # لتصدير PDF
pip install openpyxl   # لتصدير Excel
pip install celery     # للمهام المجدولة
pip install redis      # للتخزين المؤقت
```

### إعداد قاعدة البيانات
```bash
python manage.py makemigrations installations
python manage.py migrate
```

### إعداد المهام المجدولة
```bash
# تشغيل Celery Worker
celery -A your_project worker -l info

# تشغيل Celery Beat للمهام المجدولة
celery -A your_project beat -l info
```

## الاستخدام

### إضافة التطبيق إلى INSTALLED_APPS
```python
INSTALLED_APPS = [
    # ... التطبيقات الأخرى
    'installations',
    'django_celery_beat',  # للمهام المجدولة
]
```

### إضافة URLs
```python
# في urls.py الرئيسي
from django.urls import path, include

urlpatterns = [
    # ... URLs الأخرى
    path('installations/', include('installations.urls_new')),
]
```

### إعدادات إضافية
```python
# في settings.py
INSTALLATION_SETTINGS = {
    'MAX_DAILY_INSTALLATIONS': 13,
    'MAX_TECHNICIAN_WINDOWS': 20,
    'WORK_START_TIME': '08:00',
    'WORK_END_TIME': '17:00',
    'ENABLE_EMAIL_ALERTS': True,
    'ENABLE_SMS_ALERTS': False,
}

# إعدادات Celery
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# إعدادات التخزين المؤقت
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## API Endpoints

### التركيبات
- `GET /installations/api/calendar-events/` - أحداث التقويم
- `GET /installations/api/daily-details/` - تفاصيل اليوم
- `POST /installations/api/quick-update/<id>/` - تحديث سريع
- `POST /installations/api/bulk-update/` - تحديث مجمع

### التحليلات
- `GET /installations/api/analytics/dashboard/` - تحليلات لوحة التحكم
- `GET /installations/api/analytics/branch-comparison/` - مقارنة الفروع
- `GET /installations/api/analytics/monthly-report/` - التقرير الشهري

### الإنذارات
- `GET /installations/api/alerts/` - قائمة الإنذارات
- `POST /installations/api/alerts/<id>/resolve/` - حل الإنذار

### المصنع
- `POST /installations/api/factory/update-status/<id>/` - تحديث حالة الطلب
- `POST /installations/api/factory/bulk-update-status/` - تحديث مجمع للحالة
- `GET /installations/api/factory/stats/` - إحصائيات المصنع

## الميزات المتقدمة

### نظام الإنذارات
النظام يوفر إنذارات ذكية لمختلف الحالات:
- تجاوز الحد الأقصى للتركيبات اليومية (13 تركيب)
- تجاوز الحد الأقصى للشبابيك للفني الواحد (20 شباك)
- التركيبات المتأخرة
- انخفاض تقييمات الجودة
- التركيبات العاجلة

### التحليلات التنبؤية
النظام يحلل البيانات التاريخية لتوقع:
- الأحمال المستقبلية
- احتياجات الفرق
- الاتجاهات الموسمية
- نقاط الاختناق المحتملة

### التكامل مع النظام القديم
النظام يوفر أدوات للتكامل مع النظام القديم:
- مزامنة البيانات
- مقارنة الأنظمة
- هجرة البيانات

## الصيانة والمراقبة

### المهام المجدولة
- فحص الإنذارات كل ساعة
- إنشاء التقرير اليومي
- تنظيف البيانات القديمة أسبوعياً
- إرسال الملخص الأسبوعي

### مراقبة الأداء
- مراقبة استخدام ذاكرة التخزين المؤقت
- مراقبة أداء قاعدة البيانات
- مراقبة المهام المجدولة
- مراقبة الإنذارات الحرجة

## الدعم والتطوير

### السجلات (Logs)
النظام يسجل جميع العمليات المهمة:
- إنشاء وتحديث التركيبات
- تغيير الحالات
- الإنذارات الحرجة
- أخطاء النظام

### التطوير المستقبلي
- تطبيق موبايل للفنيين
- تكامل مع GPS لتتبع الموقع
- نظام إشعارات فورية
- تحليلات ذكية بالذكاء الاصطناعي

## الأمان

### حماية البيانات
- تشفير البيانات الحساسة
- تسجيل جميع العمليات
- صلاحيات مستخدمين متدرجة
- نسخ احتياطية تلقائية

### التحكم في الوصول
- مصادقة المستخدمين
- تحديد الصلاحيات حسب الدور
- مراقبة النشاطات المشبوهة
- انتهاء صلاحية الجلسات

---

تم تطوير هذا النظام لتوفير حل شامل ومتطور لإدارة التركيبات مع التركيز على الكفاءة والجودة وسهولة الاستخدام.
